// Copyright Epic Games, Inc. All Rights Reserved.

#include "Common.ush"

#if MOBILE_MULTI_VIEW
Texture2DArray InputTexture;
#else
Texture2D InputTexture;
#endif
SamplerState InputSampler;

void ScreenPassVS(
	in float4 InPosition : ATTRIBUTE0,
	in float2 InTexCoord : ATTRIBUTE1,
	out noperspective float4 OutUVAndScreenPos : TEXCOORD0
#if MOBILE_MULTI_VIEW && INSTANCED_STEREO
	, in uint InstanceId : SV_InstanceID
	, out nointerpolation uint EyeIndex : VIEW_ID
	, out uint LayerIndex : SV_RenderTargetArrayIndex
#endif
	, out float4 OutPosition : SV_POSITION
)
{
#if MOBILE_MULTI_VIEW && INSTANCED_STEREO
	EyeIndex = InstanceId;
	LayerIndex = InstanceId;
#endif
	DrawRectangle(InPosition, InTexCoord, OutPosition, OutUVAndScreenPos);
}

float4 CopyRectPS(
	noperspective float4 UVAndScreenPos : TEXCOORD0
#if MO<PERSON>LE_MULTI_VIEW && INSTANCED_STEREO
	, in nointerpolation uint EyeIndex : VIEW_ID
#elif MOBILE_MULTI_VIEW
	, in nointerpolation uint EyeIndex : SV_ViewID
#endif
	) : SV_Target0
{
	float2 UV = UVAndScreenPos.xy;
#if MOBILE_MULTI_VIEW
	return Texture2DArraySample(InputTexture, InputSampler, float3(UV, EyeIndex));
#else
	return Texture2DSample(InputTexture, InputSampler, UV);
#endif
}