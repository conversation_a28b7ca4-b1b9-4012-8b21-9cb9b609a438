{"Plugins": [{"Info": {"Types": {"UsdMtlxFileFormat": {"bases": ["SdfFileFormat"], "displayName": "USD MaterialX File Format", "extensions": ["mtlx"], "formatId": "mtlx", "primary": true, "supportsEditing": false, "supportsWriting": false, "target": "usd"}, "UsdMtlxDiscoveryPlugin": {"bases": ["NdrDiscoveryPlugin"], "displayName": "MaterialX Discovery"}, "UsdMtlxParserPlugin": {"bases": ["NdrParserPlugin"], "displayName": "MaterialX Node Parser"}}}, "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdMtlx.dylib", "Name": "usdMtlx", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}