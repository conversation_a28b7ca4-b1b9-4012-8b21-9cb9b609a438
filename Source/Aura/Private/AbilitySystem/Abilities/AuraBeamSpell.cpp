// Copyright iYuuki Studio


#include "AbilitySystem/Abilities/AuraBeamSpell.h"

#include "AuraGameplayTags.h"
#include "GameFramework/Character.h"
#include "Interaction/CombatInterface.h"
#include "Kismet/KismetSystemLibrary.h"
#include "System/AuraAbilitySystemLibrary.h"

class AAuraCharacterBase;

void UAuraBeamSpell::StoreCursorHit(const FHitResult& HitResult)
{
	StoredCursorHit = HitResult;
	if (!HitResult.bBlockingHit)
	{
		CancelAbility(CurrentSpecHandle, CurrentActorInfo, CurrentActivationInfo, true);
	}
}

void UAuraBeamSpell::StoreOwnerVariables()
{
	if (CurrentActorInfo)
	{
		OwnerPlayerController = CurrentActorInfo->PlayerController.Get();
		OwnerCharacter = Cast<ACharacter>(GetAvatarActorFromActorInfo());
	}
}

void UAuraBeamSpell::TraceFirstBlockingHit(const FVector& TargetLocation)
{
	if (OwnerCharacter->Implements<UCombatInterface>())
	{
		TArray<AActor*> ActorsToIgnore;
		ActorsToIgnore.Add(OwnerCharacter);

		FVector StartLocation = ICombatInterface::Execute_GetCombatSocketLocation(
			OwnerCharacter, FAuraGameplayTags::Get().CombatSocket_Weapon);

		FHitResult HitResult;
		UKismetSystemLibrary::SphereTraceSingle(OwnerCharacter, StartLocation, TargetLocation, 6.f,
		                                        ETraceTypeQuery::TraceTypeQuery1, false, ActorsToIgnore,
		                                        EDrawDebugTrace::None, HitResult, true);


		if (HitResult.bBlockingHit)
		{
			StoreCursorHit(HitResult);

			if (ICombatInterface* CombatInterface = Cast<ICombatInterface>(HitResult.GetActor()))
			{
				if (!CombatInterface->GetOnDeathDelegate().IsAlreadyBound(this, &ThisClass::OnPrimaryTargetDied))
				{
					CombatInterface->GetOnDeathDelegate().AddDynamic(this, &ThisClass::OnPrimaryTargetDied);
				}
			}
		}
	}
}

TArray<AActor*> UAuraBeamSpell::DiffuseBeamToClosestActors(AActor* TargetActor)
{
	TArray<AActor*> IgnoreActors;
	IgnoreActors.Add(OwnerCharacter);
	IgnoreActors.Add(TargetActor);

	TArray<AActor*> ActorsInRadius;

	UAuraAbilitySystemLibrary::GetLivePlayerWithinRadius(this, IgnoreActors,
	                                                     TargetActor->GetActorLocation(), DiffusionRadius,
	                                                     ActorsInRadius);

	TArray<AActor*> ClosestActors = UAuraAbilitySystemLibrary::GetClosestActorsFromSource(
		TargetActor, ActorsInRadius, MaxDiffusedTargets);


	for (AActor* ClosestActor : ClosestActors)
	{
		if (ICombatInterface* CombatInterface = Cast<ICombatInterface>(ClosestActor))
		{
			if (!CombatInterface->GetOnDeathDelegate().IsAlreadyBound(this, &ThisClass::OnAdditionalTargetDied))
				CombatInterface->GetOnDeathDelegate().AddDynamic(this, &ThisClass::OnAdditionalTargetDied);
		}
	}

	return ClosestActors;
}

void UAuraBeamSpell::RemoveDelegates()
{
	for (AActor* Actor : BoundDeathTargets)
	{
		if (ICombatInterface* CombatInterface = Cast<ICombatInterface>(Actor))
		{
			CombatInterface->GetOnDeathDelegate().RemoveAll(this);
		}
	}
	BoundDeathTargets.Empty();
}
