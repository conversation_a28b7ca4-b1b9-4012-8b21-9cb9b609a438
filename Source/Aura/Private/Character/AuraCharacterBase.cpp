// Copyright iYuuki Studio


#include "Character/AuraCharacterBase.h"

#include "AbilitySystemComponent.h"
#include "AuraGameplayTags.h"
#include "AbilitySystem/AuraAbilitySystemComponent.h"
#include "AbilitySystem/AuraAttributeSet.h"
#include "AbilitySystem/Debuff/DebuffNiagaraComponent.h"
#include "Aura/Aura.h"
#include "Components/CapsuleComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"

// Sets default values
AAuraCharacterBase::AAuraCharacterBase()
{
	Weapon = CreateDefaultSubobject<USkeletalMeshComponent>("Weapon");
	Weapon->SetupAttachment(GetMesh(), "WeaponHandSocket");
	Weapon->SetCollisionEnabled(ECollisionEnabled::NoCollision);

	GetMesh()->SetCollisionResponseToChannel(ECC_PROJECTILE, ECR_Overlap);

	BurnNiagaraComponent = CreateDefaultSubobject<UDebuffNiagaraComponent>("BurnNiagaraComponent");
	BurnNiagaraComponent->SetupAttachment(GetRootComponent());
	StunNiagaraComponent = CreateDefaultSubobject<UDebuffNiagaraComponent>("StunNiagaraComponent");
	StunNiagaraComponent->SetupAttachment(GetRootComponent());
}

UAbilitySystemComponent* AAuraCharacterBase::GetAbilitySystemComponent() const
{
	return AbilitySystemComponent;
}

void AAuraCharacterBase::Die(const FVector& DeathImpulse)
{
	Weapon->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
	MulticastHandleDeath(DeathImpulse);
	OnDie.Broadcast();
	OnDeath.Broadcast(this);
}

void AAuraCharacterBase::MulticastHandleDeath_Implementation(const FVector& DeathImpulse)
{
	if (DeathSound)
		UGameplayStatics::PlaySoundAtLocation(this, DeathSound, GetActorLocation());

	Weapon->SetSimulatePhysics(true);
	Weapon->SetEnableGravity(true);
	Weapon->SetCollisionEnabled(ECollisionEnabled::PhysicsOnly);
	Weapon->AddImpulse(DeathImpulse * .1f, NAME_None, true);

	GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::NoCollision);

	GetMesh()->SetSimulatePhysics(true);
	GetMesh()->SetEnableGravity(true);
	GetMesh()->SetCollisionEnabled(ECollisionEnabled::PhysicsOnly);
	GetMesh()->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Block);
	GetMesh()->AddImpulse(DeathImpulse, NAME_None, true);

	BurnNiagaraComponent->Deactivate();
	StunNiagaraComponent->Deactivate();
}

UAnimMontage* AAuraCharacterBase::GetHitReactMontage_Implementation() const
{
	return HitReactMontage;
}

TArray<FTaggedMontage> AAuraCharacterBase::GetAttackMontages_Implementation()
{
	return AttackMontages;
}

UNiagaraSystem* AAuraCharacterBase::GetBloodEffect_Implementation()
{
	return BloodEffect;
}

FTaggedMontage AAuraCharacterBase::GetTaggedMontageByTag_Implementation(const FGameplayTag& Tag)
{
	for (const FTaggedMontage& TaggedMontage : AttackMontages)
	{
		if (TaggedMontage.MontageTag == Tag)
		{
			return TaggedMontage;
		}
	}
	return FTaggedMontage();
}

int32 AAuraCharacterBase::GetMinionCount_Implementation()
{
	return SpawnedMinionCount;
}

void AAuraCharacterBase::IncrementMinionCount_Implementation(int32 NumToIncrement)
{
	SpawnedMinionCount += NumToIncrement;
}

ECharacterClass AAuraCharacterBase::GetCharacterClass_Implementation()
{
	return CharacterClass;
}

int32 AAuraCharacterBase::GetCharacterLevel_Implementation()
{
	return ICombatInterface::GetCharacterLevel_Implementation();
}

void AAuraCharacterBase::GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(AAuraCharacterBase, bStunned);
	DOREPLIFETIME(AAuraCharacterBase, bShocked);
}

// Called when the game starts or when spawned
void AAuraCharacterBase::BeginPlay()
{
	Super::BeginPlay();
}

void AAuraCharacterBase::InitAbilityActorInfo()
{
}

void AAuraCharacterBase::InitialAttributes()
{
	ApplyAttributes(DefaultPrimaryAttributes);
	ApplyAttributes(DefaultSecondaryAttributes);
	ApplyAttributes(DefaultVitalAttributes);
}

FVector AAuraCharacterBase::GetCombatSocketLocation_Implementation(FGameplayTag AttackTag)
{
	FAuraGameplayTags GameplayTags = FAuraGameplayTags::Get();
	if (AttackTag == GameplayTags.CombatSocket_Weapon && IsValid(Weapon))
	{
		return Weapon->GetSocketLocation(WeaponTipSocketName);
	}
	else if (AttackTag == GameplayTags.CombatSocket_LeftHand)
	{
		return GetMesh()->GetSocketLocation(LeftHandSocketName);
	}
	else if (AttackTag == GameplayTags.CombatSocket_RightHand)
	{
		return GetMesh()->GetSocketLocation(RightHandSocketName);
	}
	else if (AttackTag == GameplayTags.CombatSocket_Tail)
	{
		return GetMesh()->GetSocketLocation(TailSocketName);
	}

	return FVector::ZeroVector;
}


void AAuraCharacterBase::AddCharacterAbilities()
{
	if (!HasAuthority()) return;
	CastChecked<UAuraAbilitySystemComponent>(AbilitySystemComponent)->AddCharacterAbilities(StartupAbilities);
	AddCharacterPassiveAbilities();
}

void AAuraCharacterBase::AddCharacterPassiveAbilities()
{
	if (!HasAuthority()) return;
	CastChecked<UAuraAbilitySystemComponent>(AbilitySystemComponent)->AddCharacterPassiveAbilities(PassiveAbilities);
}

float AAuraCharacterBase::TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent,
	class AController* EventInstigator, AActor* DamageCauser)
{
	OnRadialDamage.Broadcast(DamageAmount);
	return Super::TakeDamage(DamageAmount, DamageEvent, EventInstigator, DamageCauser);
}

bool AAuraCharacterBase::IsDead_Implementation()
{
	UAuraAttributeSet* AuraAttributeSet = Cast<UAuraAttributeSet>(AttributeSet);
	if (!AuraAttributeSet) return false;

	return AuraAttributeSet->GetHealth() <= 0;
}

void AAuraCharacterBase::Dissolve_Implementation()
{
	if (DissolveMaterialInst)
	{
		UMaterialInstanceDynamic* DynamicInst = UMaterialInstanceDynamic::Create(DissolveMaterialInst, this);
		GetMesh()->SetMaterial(0, DynamicInst);
		StartDissolveTimeLine(DynamicInst);
	}
	if (WeaponDissolveMaterialInst)
	{
		UMaterialInstanceDynamic* DynamicInstWeapon =
			UMaterialInstanceDynamic::Create(WeaponDissolveMaterialInst, this);
		Weapon->SetMaterial(0, DynamicInstWeapon);
		StartWeaponDissolveTimeLine(DynamicInstWeapon);
	}
}

void AAuraCharacterBase::ApplyAttributes(TSubclassOf<UGameplayEffect> Attributes)
{
	check(GetAbilitySystemComponent())
	check(Attributes)

	FGameplayEffectContextHandle EffectContextHandle = GetAbilitySystemComponent()->MakeEffectContext();
	EffectContextHandle.AddSourceObject(this);
	const FGameplayEffectSpecHandle SpecHandle = GetAbilitySystemComponent()->MakeOutgoingSpec(
		Attributes, 1, EffectContextHandle);
	GetAbilitySystemComponent()->ApplyGameplayEffectSpecToTarget(*SpecHandle.Data.Get(), GetAbilitySystemComponent());
}

void AAuraCharacterBase::OnRep_Stunned()
{
}

void AAuraCharacterBase::OnRep_Shocked()
{
}
