// Copyright iYuuki Studio


#include "System/AuraAbilitySystemLibrary.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AuraAbilityTypes.h"
#include "AuraGameplayTags.h"
#include "AbilitySystem/AuraAbilitySystemComponent.h"
#include "Character/AuraCharacter.h"
#include "GameMode/AuraGameModeBase.h"
#include "GameplayEffectComponents/TargetTagsGameplayEffectComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Player/AuraPlayerState.h"
#include "UI/HUD/AuraHUD.h"
#include "UI/WidgetController/AuraWidgetController.h"

UOverlayWidgetController* UAuraAbilitySystemLibrary::GetOverlayWidgetController(const UObject* WorldContextObject)
{
	AAuraHUD* AuraHUD = nullptr;
	FWidgetControllerParams WCParams = GetOverlayWidgetControllerParams(WorldContextObject, AuraHUD);
	if (AuraHUD == nullptr)
	{
		return nullptr;
	}
	return AuraHUD->GetOverlayWidgetController(WCParams);
}

UAttributeMenuWidgetController* UAuraAbilitySystemLibrary::GetAttributeMenuWidgetController(
	const UObject* WorldContextObject)
{
	AAuraHUD* AuraHUD = nullptr;
	FWidgetControllerParams WCParams = GetOverlayWidgetControllerParams(WorldContextObject, AuraHUD);
	if (AuraHUD == nullptr)
	{
		return nullptr;
	}
	return AuraHUD->GetAttributeMenuWidgetController(WCParams);
}

USpellMenuWidgetController* UAuraAbilitySystemLibrary::GetSpellMenuWidgetController(const UObject* WorldContextObject)
{
	AAuraHUD* AuraHUD = nullptr;
	FWidgetControllerParams WCParams = GetOverlayWidgetControllerParams(WorldContextObject, AuraHUD);
	if (AuraHUD == nullptr)
	{
		return nullptr;
	}
	return AuraHUD->GetSpellMenuWidgetController(WCParams);
}

UAuraCharacterClassInfo* UAuraAbilitySystemLibrary::GetCharacterClassInfo(const UObject* WorldContextObject)
{
	AAuraGameModeBase* AuraGameMode = Cast<AAuraGameModeBase>(UGameplayStatics::GetGameMode(WorldContextObject));
	if (!AuraGameMode) return nullptr;
	return AuraGameMode->CharacterClassInfo;
}

UAbilityInfo* UAuraAbilitySystemLibrary::GetAbilityInformation(const UObject* WorldContextObject)
{
	AAuraGameModeBase* AuraGameMode = Cast<AAuraGameModeBase>(UGameplayStatics::GetGameMode(WorldContextObject));
	if (!AuraGameMode) return nullptr;
	return AuraGameMode->AbilityInfo;
}


void UAuraAbilitySystemLibrary::InitializeDefaultAttributes(const UObject* WorldContextObject,
                                                            const ECharacterClass& CharacterClass, float Level,
                                                            UAbilitySystemComponent* ASC)
{
	UAuraCharacterClassInfo* CharacterClassInfo = GetCharacterClassInfo(WorldContextObject);
	FCharacterClassDefaultInfo ClassDefaultInfo = CharacterClassInfo->GetClassDefaultInfo(CharacterClass);
	if (!ClassDefaultInfo.PrimaryAttributes) return;
	if (!CharacterClassInfo->SecondaryAttributes) return;
	if (!CharacterClassInfo->VitalAttributes) return;

	FGameplayEffectContextHandle PrimaryAttributesContextHandle = ASC->MakeEffectContext();
	PrimaryAttributesContextHandle.AddSourceObject(ASC->GetAvatarActor());
	FGameplayEffectSpecHandle PrimaryAttributesSpecHandle = ASC->MakeOutgoingSpec(
		ClassDefaultInfo.PrimaryAttributes, Level, PrimaryAttributesContextHandle);
	ASC->ApplyGameplayEffectSpecToSelf(*PrimaryAttributesSpecHandle.Data.Get());

	FGameplayEffectContextHandle SecondaryAttributesContextHandle = ASC->MakeEffectContext();
	SecondaryAttributesContextHandle.AddSourceObject(ASC->GetAvatarActor());
	FGameplayEffectSpecHandle SecondaryAttributesSpecHandle = ASC->MakeOutgoingSpec(
		CharacterClassInfo->SecondaryAttributes, Level, SecondaryAttributesContextHandle);
	ASC->ApplyGameplayEffectSpecToSelf(*SecondaryAttributesSpecHandle.Data.Get());

	FGameplayEffectContextHandle VitalAttributesContextHandle = ASC->MakeEffectContext();
	VitalAttributesContextHandle.AddSourceObject(ASC->GetAvatarActor());
	FGameplayEffectSpecHandle VitalAttributesSpecHandle = ASC->MakeOutgoingSpec(
		CharacterClassInfo->VitalAttributes, Level, VitalAttributesContextHandle);
	ASC->ApplyGameplayEffectSpecToSelf(*VitalAttributesSpecHandle.Data.Get());
}

void UAuraAbilitySystemLibrary::GiveStartupAbilities(const UObject* WorldContextObject,
                                                     UAbilitySystemComponent* ASC, ECharacterClass CharacterClass)
{
	UAuraCharacterClassInfo* CharacterClassInfo = GetCharacterClassInfo(WorldContextObject);

	for (const auto& StartupAbility : CharacterClassInfo->StartupAbilities)
	{
		FGameplayAbilitySpec AbilitySpec = FGameplayAbilitySpec(StartupAbility, 1);

		ASC->GiveAbility(AbilitySpec);
	}


	const FCharacterClassDefaultInfo& ClassDefaultInfo = CharacterClassInfo->GetClassDefaultInfo(CharacterClass);
	if (ClassDefaultInfo.StartupAbilities.Num() > 0)
	{
		int CharacterLevel = 1;
		if (ASC->GetAvatarActor()->Implements<UCombatInterface>())
		{
			CharacterLevel = ICombatInterface::Execute_GetCharacterLevel(ASC->GetAvatarActor());
		}

		for (const auto& StartupAbility : ClassDefaultInfo.StartupAbilities)
		{
			FGameplayAbilitySpec AbilitySpec = FGameplayAbilitySpec(StartupAbility,
			                                                        CharacterLevel);
			ASC->GiveAbility(AbilitySpec);
		}
	}
}

UCurveTable* UAuraAbilitySystemLibrary::GetDamageCalculationData(const UObject* WorldContextObject)
{
	UAuraCharacterClassInfo* CharacterClassInfo = GetCharacterClassInfo(WorldContextObject);

	return CharacterClassInfo->DamageCalculationCurve;
}

bool UAuraAbilitySystemLibrary::IsBlockedHit(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->IsBlockedHit();
	return false;
}

bool UAuraAbilitySystemLibrary::IsCriticalHit(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->IsCriticalHit();
	return false;
}

bool UAuraAbilitySystemLibrary::IsSuccessfulDebuff(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->IsSuccessfulDebuff();
	return false;
}

bool UAuraAbilitySystemLibrary::IsSuccessfulKnockback(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->IsSuccessfulKnockback();
	return false;
}

float UAuraAbilitySystemLibrary::GetDebuffDamage(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->GetDebuffDamage();
	return 0.f;
}

float UAuraAbilitySystemLibrary::GetDebuffDuration(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->GetDebuffDuration();
	return 0.f;
}

float UAuraAbilitySystemLibrary::GetDebuffFrequency(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->GetDebuffFrequency();
	return 0.f;
}

FGameplayTag UAuraAbilitySystemLibrary::GetDamageType(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext)
	{
		if (AuraEffectContext->GetDamageType().IsValid())
		{
			return *AuraEffectContext->GetDamageType();
		}
	}
	return FGameplayTag();
}

FVector UAuraAbilitySystemLibrary::GetDeathImpulse(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext)
	{
		return AuraEffectContext->GetDeathImpulse();
	}
	return FVector();
}

FVector UAuraAbilitySystemLibrary::GetKnockback(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext)
	{
		return AuraEffectContext->GetKnockback();
	}
	return FVector();
}

bool UAuraAbilitySystemLibrary::GetIsRadialDamage(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->IsRadialDamage();
	return false;
}

float UAuraAbilitySystemLibrary::GetMinDamageRadius(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->GetMinDamageRadius();
	return 0.f;
}

float UAuraAbilitySystemLibrary::GetMaxDamageRadius(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) return AuraEffectContext->GetMaxDamageRadius();
	return 0.f;
}

FVector UAuraAbilitySystemLibrary::GetDamageOrigin(const FGameplayEffectContextHandle& EffectContextHandle)
{
	const FAuraGameplayEffectContext* AuraEffectContext = static_cast<const FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext)
	{
		return AuraEffectContext->GetDamageOrigin();
	}
	return FVector();
}


void UAuraAbilitySystemLibrary::SetIsBlockedHit(FGameplayEffectContextHandle& EffectContextHandle, bool bInIsBlockedHit)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetIsBlockedHit(bInIsBlockedHit);
}

void UAuraAbilitySystemLibrary::SetIsCriticalHit(FGameplayEffectContextHandle& EffectContextHandle,
                                                 bool bInIsCriticalHit)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetIsCriticalHit(bInIsCriticalHit);
}

void UAuraAbilitySystemLibrary::SetIsSuccessfulDebuff(FGameplayEffectContextHandle& EffectContextHandle,
                                                      bool bInIsSuccessfulDebuff)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetIsSuccessfulDebuff(bInIsSuccessfulDebuff);
}

void UAuraAbilitySystemLibrary::SetIsSuccessfulKnockback(FGameplayEffectContextHandle& EffectContextHandle,
                                                         bool bInIsSuccessfulKnockback)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetIsSuccessfulKnockback(bInIsSuccessfulKnockback);
}

void UAuraAbilitySystemLibrary::SetDebuffDamage(FGameplayEffectContextHandle& EffectContextHandle, float InDebuffDamage)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetDebuffDamage(InDebuffDamage);
}

void UAuraAbilitySystemLibrary::SetDebuffDuration(FGameplayEffectContextHandle& EffectContextHandle,
                                                  float InDebuffDuration)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetDebuffDuration(InDebuffDuration);
}

void UAuraAbilitySystemLibrary::SetDebuffFrequency(FGameplayEffectContextHandle& EffectContextHandle,
                                                   float InDebuffFrequency)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetDebuffFrequency(InDebuffFrequency);
}

void UAuraAbilitySystemLibrary::SetDamageType(FGameplayEffectContextHandle& EffectContextHandle,
                                              FGameplayTag InDamageType)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetDamageType(InDamageType);
}

void UAuraAbilitySystemLibrary::SetDeathImpulse(FGameplayEffectContextHandle& EffectContextHandle,
                                                const FVector& InDeathImpulse)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetDeathImpulse(InDeathImpulse);
}

void UAuraAbilitySystemLibrary::SetKnockback(FGameplayEffectContextHandle& EffectContextHandle,
                                             const FVector& InKnockback)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetKnockback(InKnockback);
}


void UAuraAbilitySystemLibrary::SetIsRadialDamage(FGameplayEffectContextHandle& EffectContextHandle,
                                                  bool bInIsRadialDamage)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetIsRadialDamage(bInIsRadialDamage);
}

void UAuraAbilitySystemLibrary::SetMinDamageRadius(FGameplayEffectContextHandle& EffectContextHandle,
                                                   float InMinDamageRadius)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetMinDamageRadius(InMinDamageRadius);
}

void UAuraAbilitySystemLibrary::SetMaxDamageRadius(FGameplayEffectContextHandle& EffectContextHandle,
                                                   float InMaxDamageRadius)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetMaxDamageRadius(InMaxDamageRadius);
}

void UAuraAbilitySystemLibrary::SetDamageOrigin(FGameplayEffectContextHandle& EffectContextHandle,
                                                const FVector& InDamageOrigin)
{
	FAuraGameplayEffectContext* AuraEffectContext = static_cast<FAuraGameplayEffectContext*>(
		EffectContextHandle.Get());
	if (AuraEffectContext) AuraEffectContext->SetDamageOrigin(InDamageOrigin);
}

void UAuraAbilitySystemLibrary::GetLivePlayerWithinRadius(const UObject* WorldContextObject,
                                                          const TArray<AActor*>& IgnoreActors, const FVector& Origin,
                                                          float Radius, TArray<AActor*>& OutActors)
{
	// Reference: ApplyRadialDamageWithFalloff

	FCollisionQueryParams CollisionQueryParams;
	CollisionQueryParams.AddIgnoredActors(IgnoreActors);

	TArray<FOverlapResult> Overlaps;
	if (UWorld* World = GEngine->GetWorldFromContextObject(WorldContextObject, EGetWorldErrorMode::LogAndReturnNull))
	{
		World->OverlapMultiByObjectType(Overlaps, Origin, FQuat::Identity,
		                                FCollisionObjectQueryParams(FCollisionObjectQueryParams::InitType::AllObjects),
		                                FCollisionShape::MakeSphere(Radius), CollisionQueryParams);
		for (FOverlapResult const& Overlap : Overlaps)
		{
			AActor* OverlapActor = Overlap.GetActor();
			if (OverlapActor->Implements<UCombatInterface>() && !ICombatInterface::Execute_IsDead(OverlapActor))
			{
				OutActors.AddUnique(OverlapActor);
			}
		}
	}
}

int32 UAuraAbilitySystemLibrary::GetXPRewardForClassAndLevel(UObject* WorldContextObject,
                                                             const ECharacterClass& CharacterClass,
                                                             int32 CharacterLevel)
{
	UAuraCharacterClassInfo* CharacterClassInfo = GetCharacterClassInfo(WorldContextObject);
	if (!CharacterClassInfo) return 0;
	return CharacterClassInfo->GetClassDefaultInfo(CharacterClass).XPToGive.GetValueAtLevel(CharacterLevel);
}

FGameplayEffectContextHandle UAuraAbilitySystemLibrary::ApplyDamageEffect(const FDamageEffectParams& Params, const FVector& InDamageOrigin)
{
	if (!Params.DamageGameplayEffectClass || Params.TargetAbilitySystemComponent == nullptr)
		return FGameplayEffectContextHandle();

	if (Params.TargetAbilitySystemComponent->GetAvatarActor()->Implements<UCombatInterface>())
	{
		bool bIsDead = ICombatInterface::Execute_IsDead(Params.TargetAbilitySystemComponent->GetAvatarActor());
		if (bIsDead) return FGameplayEffectContextHandle();
	}


	FGameplayEffectContextHandle ContextHandle = Params.SourceAbilitySystemComponent->MakeEffectContext();
	ContextHandle.AddSourceObject(Params.SourceAbilitySystemComponent->GetAvatarActor());
	const FGameplayEffectSpecHandle SpecHandle = Params.SourceAbilitySystemComponent->MakeOutgoingSpec(
		Params.DamageGameplayEffectClass, Params.AbilityLevel, ContextHandle);

	SetDeathImpulse(ContextHandle, Params.DeathImpulse);
	SetKnockback(ContextHandle, Params.Knockback);
	SetDamageType(ContextHandle, Params.DamageType);
	SetIsRadialDamage(ContextHandle, Params.bIsRadialDamage);
	SetMinDamageRadius(ContextHandle, Params.MinDamageRadius);
	SetMaxDamageRadius(ContextHandle, Params.MaxDamageRadius);
	SetDamageOrigin(ContextHandle, InDamageOrigin);

	FAuraGameplayTags GameplayTags = FAuraGameplayTags::Get();
	SpecHandle.Data->SetSetByCallerMagnitude(Params.DamageType, Params.BaseDamage);
	SpecHandle.Data->SetSetByCallerMagnitude(GameplayTags.Debuff_Info_Damage, Params.DebuffDamage);
	SpecHandle.Data->SetSetByCallerMagnitude(GameplayTags.Debuff_Info_Duration, Params.DebuffDuration);
	SpecHandle.Data->SetSetByCallerMagnitude(GameplayTags.Debuff_Info_Frequency, Params.DebuffFrequency);
	SpecHandle.Data->SetSetByCallerMagnitude(GameplayTags.Debuff_Info_Chance, Params.DebuffChance);
	SpecHandle.Data->SetSetByCallerMagnitude(GameplayTags.Knockback_Chance, Params.KnockbackChance);

	Params.TargetAbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
	return ContextHandle;
}

TArray<AActor*> UAuraAbilitySystemLibrary::GetLiveTargetsWithinRadius(const UObject* WorldContextObject,
                                                                      const TArray<AActor*>& IgnoreActors,
                                                                      const FVector& Origin, float Radius)
{
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContextObject, EGetWorldErrorMode::LogAndReturnNull);
	if (!World) return TArray<AActor*>();

	TArray<AActor*> ActorsToIgnore = IgnoreActors;

	TArray<AActor*> Actors;
	TArray<struct FOverlapResult> OverlapResults;

	World->OverlapMultiByObjectType(OverlapResults, Origin, FQuat::Identity,
	                                FCollisionObjectQueryParams(FCollisionObjectQueryParams::InitType::AllObjects),
	                                FCollisionShape::MakeSphere(Radius));

	for (const auto& OverlapResult : OverlapResults)
	{
		AActor* OverlapActor = OverlapResult.GetActor();
		if (OverlapActor->Implements<UCombatInterface>() && !ICombatInterface::Execute_IsDead(OverlapActor))
		{
			Actors.AddUnique(OverlapActor);
		}
	}

	return Actors;
}

TArray<AActor*> UAuraAbilitySystemLibrary::GetClosestActorsFromSource(
	const AActor* SourceActor,
	const TArray<AActor*>& TargetActors,
	int32 MaxTargets)
{
	TArray<AActor*> ClosestActors;

	if (!SourceActor || TargetActors.Num() == 0 || MaxTargets <= 0)
	{
		return ClosestActors;
	}

	FVector SourceLocation = SourceActor->GetActorLocation();

	// 拷贝数组，以便排序
	TArray<AActor*> SortedTargets = TargetActors;

	// 排序：按距离升序排列
	SortedTargets.Sort([&](const AActor& A, const AActor& B)
	{
		float DistA = FVector::DistSquared(SourceLocation, A.GetActorLocation());
		float DistB = FVector::DistSquared(SourceLocation, B.GetActorLocation());
		return DistA < DistB;
	});

	// 添加前 N 个目标
	int32 NumToAdd = FMath::Min(MaxTargets, SortedTargets.Num());
	for (int32 i = 0; i < NumToAdd; ++i)
	{
		ClosestActors.Add(SortedTargets[i]);
	}

	return ClosestActors;
}

bool UAuraAbilitySystemLibrary::AddLooseGameplayTagsToActor(
	AActor* Actor, const FGameplayTagContainer& GameplayTags)
{
	UAbilitySystemComponent* ASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Actor);
	if (!IsValid(ASC))
		return false;

	UAbilitySystemBlueprintLibrary::AddLooseGameplayTags(Actor, GameplayTags, false);
	return true;
}

bool UAuraAbilitySystemLibrary::RemoveLooseGameplayTagsToActor(AActor* Actor, const FGameplayTagContainer& GameplayTags)
{
	UAbilitySystemComponent* ASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Actor);
	if (!IsValid(ASC)) return false;

	UAbilitySystemBlueprintLibrary::RemoveLooseGameplayTags(Actor, GameplayTags, false);
	return true;
}

bool UAuraAbilitySystemLibrary::IsNotFriend(AActor* FirstActor, AActor* SecondActor)
{
	bool bIsAllPlayer = FirstActor->ActorHasTag(FName("Player")) && SecondActor->ActorHasTag(FName("Player"));
	bool bIsAllEnemy = FirstActor->ActorHasTag(FName("Enemy")) && SecondActor->ActorHasTag(FName("Enemy"));
	return !bIsAllPlayer && !bIsAllEnemy;
}

FWidgetControllerParams UAuraAbilitySystemLibrary::GetOverlayWidgetControllerParams(
	const UObject* WorldContextObject, AAuraHUD*& OutAuraHUD)
{
	APlayerController* PlayerController = UGameplayStatics::GetPlayerController(WorldContextObject, 0);
	if (PlayerController)
	{
		AHUD* HUD = PlayerController->GetHUD();
		if (HUD)
		{
			AAuraHUD* AuraHUD = Cast<AAuraHUD>(HUD);
			if (AuraHUD)
			{
				OutAuraHUD = AuraHUD;
				AAuraPlayerState* PlayerState = PlayerController->GetPlayerState<AAuraPlayerState>();
				UAbilitySystemComponent* ASC = PlayerState->GetAbilitySystemComponent();
				UAttributeSet* AttributeSet = PlayerState->GetAttributeSet();
				FWidgetControllerParams WCParams(PlayerController, PlayerState, ASC, AttributeSet);
				return WCParams;
			}
		}
	}
	return FWidgetControllerParams();
}
