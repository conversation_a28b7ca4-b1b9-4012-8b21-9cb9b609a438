#pragma once
#include "GameplayEffectTypes.h"
#include "AuraAbilityTypes.generated.h"

class UGameplayEffect;

USTRUCT(BlueprintType)
struct FDamageEffectParams
{
	GENERATED_BODY()

	FDamageEffectParams()
	{
	};

public:
	UPROPERTY()
	UObject* WorldContextObject = nullptr;
	UPROPERTY()
	TSubclassOf<UGameplayEffect> DamageGameplayEffectClass = nullptr;
	UPROPERTY()
	UAbilitySystemComponent* SourceAbilitySystemComponent = nullptr;
	UPROPERTY()
	UAbilitySystemComponent* TargetAbilitySystemComponent = nullptr;

	UPROPERTY()
	float BaseDamage = 0.f;
	UPROPERTY()
	float AbilityLevel = 1.f;
	UPROPERTY()
	FGameplayTag DamageType = FGameplayTag();
	UPROPERTY()
	float DeathImpulseMagnitude = 0.f;
	UPROPERTY()
	float KnockbackMagnitude = 0.f;
	UPROPERTY()
	float KnockbackChance = 0.f;
	UPROPERTY()
	FVector DeathImpulse = FVector::ZeroVector;
	UPROPERTY()
	FVector Knockback = FVector::ZeroVector;

	UPROPERTY()
	float DebuffChance = 0.f;
	UPROPERTY()
	float DebuffDamage = 0.f;
	UPROPERTY()
	float DebuffDuration = 0.f;
	UPROPERTY()
	float DebuffFrequency = 0.f;

	UPROPERTY()
	bool bIsRadialDamage = false;
	UPROPERTY()
	float MinDamageRadius = 0.f;
	UPROPERTY()
	float MaxDamageRadius = 0.f;
};

USTRUCT(BlueprintType)
struct FAuraGameplayEffectContext : public FGameplayEffectContext
{
	GENERATED_BODY()

public:
	bool IsCriticalHit() const { return bIsCriticalHit; }
	bool IsBlockedHit() const { return bIsBlockedHit; }
	bool IsSuccessfulDebuff() const { return bIsSuccessfulDebuff; }
	bool IsSuccessfulKnockback() const { return bIsSuccessfulKnockback; }
	TSharedPtr<FGameplayTag> GetDamageType() const { return DamageType; }
	float GetDebuffDamage() const { return DebuffDamage; }
	float GetDebuffDuration() const { return DebuffDuration; }
	float GetDebuffFrequency() const { return DebuffFrequency; }
	FVector GetDeathImpulse() const { return DeathImpulse; }
	FVector GetKnockback() const { return Knockback; }
	bool IsRadialDamage() const { return bIsRadialDamage; }
	float GetMinDamageRadius() const { return MinDamageRadius; }
	float GetMaxDamageRadius() const { return MaxDamageRadius; }
	FVector GetDamageOrigin() const { return DamageOrigin; }

	void SetIsCriticalHit(bool bInIsCriticalHit) { bIsCriticalHit = bInIsCriticalHit; }
	void SetIsBlockedHit(bool bInIsBlockedHit) { bIsBlockedHit = bInIsBlockedHit; }
	void SetIsSuccessfulDebuff(bool bInIsSuccessfulDebuff) { bIsSuccessfulDebuff = bInIsSuccessfulDebuff; }
	void SetIsSuccessfulKnockback(bool bInIsSuccessfulKnockback) { bIsSuccessfulKnockback = bInIsSuccessfulKnockback; }
	void SetDamageType(FGameplayTag InDamageType) { DamageType = MakeShared<FGameplayTag>(InDamageType); }
	void SetDebuffDamage(float InDebuffDamage) { DebuffDamage = InDebuffDamage; }
	void SetDebuffDuration(float InDebuffDuration) { DebuffDuration = InDebuffDuration; }
	void SetDebuffFrequency(float InDebuffFrequency) { DebuffFrequency = InDebuffFrequency; }
	void SetDeathImpulse(const FVector& InDeathImpulse) { DeathImpulse = InDeathImpulse; }
	void SetKnockback(const FVector& InKnockback) { Knockback = InKnockback; }
	void SetIsRadialDamage(bool bInIsRadialDamage) { bIsRadialDamage = bInIsRadialDamage; }
	void SetMinDamageRadius(float InMinDamageRadius) { MinDamageRadius = InMinDamageRadius; }
	void SetMaxDamageRadius(float InMaxDamageRadius) { MaxDamageRadius = InMaxDamageRadius; }
	void SetDamageOrigin(const FVector& InDamageOrigin) { DamageOrigin = InDamageOrigin; }

	/** Returns the actual struct used for serialization, subclasses must override this! */
	virtual UScriptStruct* GetScriptStruct() const override
	{
		return StaticStruct();
	}

	virtual bool NetSerialize(FArchive& Ar, class UPackageMap* Map, bool& bOutSuccess) override;

	/** Creates a copy of this context, used to duplicate for later modifications */
	virtual FAuraGameplayEffectContext* Duplicate() const override
	{
		FAuraGameplayEffectContext* NewContext = new FAuraGameplayEffectContext();
		*NewContext = *this;
		if (GetHitResult())
		{
			// Does a deep copy of the hit result
			NewContext->AddHitResult(*GetHitResult(), true);
		}
		return NewContext;
	}

protected:
	UPROPERTY();
	bool bIsCriticalHit = false;
	UPROPERTY();
	bool bIsBlockedHit = false;
	UPROPERTY();
	bool bIsSuccessfulDebuff = false;
	UPROPERTY();
	bool bIsSuccessfulKnockback = false;
	UPROPERTY();
	float DebuffDamage = 0.f;
	UPROPERTY();
	float DebuffDuration = 0.f;
	UPROPERTY();
	float DebuffFrequency = 0.f;
	UPROPERTY()
	FVector DeathImpulse = FVector::ZeroVector;
	UPROPERTY()
	FVector Knockback = FVector::ZeroVector;
	UPROPERTY()
	bool bIsRadialDamage = false;
	UPROPERTY()
	float MinDamageRadius = 0.f;
	UPROPERTY()
	float MaxDamageRadius = 0.f;
	UPROPERTY()
	FVector DamageOrigin = FVector::ZeroVector;


	TSharedPtr<FGameplayTag> DamageType = nullptr;
};

template <>
struct TStructOpsTypeTraits<FAuraGameplayEffectContext> : public TStructOpsTypeTraitsBase2<FAuraGameplayEffectContext>
{
	enum
	{
		WithNetSerializer = true,
		WithCopy = true // Necessary so that TSharedPtr<FHitResult> Data is copied around
	};
};
