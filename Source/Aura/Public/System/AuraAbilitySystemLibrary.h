// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "AbilitySystem/Data/AbilityInfo.h"
#include "AbilitySystem/Data/AuraCharacterClassInfo.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "AuraAbilitySystemLibrary.generated.h"

struct FActiveGameplayEffectHandle;
struct FDamageEffectParams;
class USpellMenuWidgetController;
struct FGameplayEffectContextHandle;
class UGameplayAbility;
class UAbilitySystemComponent;
class UEnemyWidgetController;
class AAuraHUD;
class UAttributeMenuWidgetController;
class UOverlayWidgetController;
class UAuraWidgetController;
struct FWidgetControllerParams;
/**
 * 
 */
UCLASS()
class AURA_API UAuraAbilitySystemLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/* Widget Controllers */
	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|WidgetController",
		meta = (DefaultToSelf = "WorldContextObject"))
	static UOverlayWidgetController* GetOverlayWidgetController(const UObject* WorldContextObject);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|WidgetController",
		meta = (DefaultToSelf = "WorldContextObject"))
	static UAttributeMenuWidgetController* GetAttributeMenuWidgetController(const UObject* WorldContextObject);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|WidgetController",
		meta = (DefaultToSelf = "WorldContextObject"))
	static USpellMenuWidgetController* GetSpellMenuWidgetController(const UObject* WorldContextObject);
	/* WIdget Controllers End */

	/* Default Infos */
	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|DefaultInfo")
	static UAuraCharacterClassInfo* GetCharacterClassInfo(const UObject* WorldContextObject);

	UFUNCTION(BlueprintPure, Category ="AuraAbilitySytemLibrary|AbilityInfo",
		meta = (DefaultToSelf = "WorldContextObject"))
	static UAbilityInfo* GetAbilityInformation(const UObject* WorldContextObject);

	UFUNCTION(Category = "AuraAbilitySystemLibrary|DefaultInfo")
	static void InitializeDefaultAttributes(const UObject* WorldContextObject,
	                                        const ECharacterClass& CharacterClass, float Level,
	                                        UAbilitySystemComponent* ASC);

	UFUNCTION(Category = "AuraAbilitySystemLibrary|DefaultInfo")
	static void GiveStartupAbilities(const UObject* WorldContextObject,
	                                 UAbilitySystemComponent* ASC, ECharacterClass CharacterClass);

	UFUNCTION(Category = "AuraAbilitySystemLibrary|DefaultInfo")
	static UCurveTable* GetDamageCalculationData(const UObject* WorldContextObject);
	/* Default Infos End */

	/* Combat */
	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static bool IsBlockedHit(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static bool IsCriticalHit(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static bool IsSuccessfulDebuff(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static bool IsSuccessfulKnockback(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static float GetDebuffDamage(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static float GetDebuffDuration(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static float GetDebuffFrequency(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static FGameplayTag GetDamageType(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static FVector GetDeathImpulse(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static FVector GetKnockback(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static bool GetIsRadialDamage(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static float GetMinDamageRadius(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static float GetMaxDamageRadius(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static FVector GetDamageOrigin(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetIsBlockedHit(UPARAM(ref)
	                            FGameplayEffectContextHandle& EffectContextHandle, bool bInIsBlockedHit);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetIsCriticalHit(UPARAM(ref)
	                             FGameplayEffectContextHandle& EffectContextHandle, bool bInIsCriticalHit);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetIsSuccessfulDebuff(UPARAM(ref)
	                                  FGameplayEffectContextHandle& EffectContextHandle, bool bInIsSuccessfulDebuff);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetIsSuccessfulKnockback(UPARAM(ref)
	                                     FGameplayEffectContextHandle& EffectContextHandle,
	                                     bool bInIsSuccessfulKnockback);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetDebuffDamage(UPARAM(ref)
	                            FGameplayEffectContextHandle& EffectContextHandle, float InDebuffDamage);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetDebuffDuration(UPARAM(ref)
	                              FGameplayEffectContextHandle& EffectContextHandle, float InDebuffDuration);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetDebuffFrequency(UPARAM(ref)
	                               FGameplayEffectContextHandle& EffectContextHandle, float InDebuffFrequency);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetDamageType(UPARAM(ref)
	                          FGameplayEffectContextHandle& EffectContextHandle, FGameplayTag InDamageType);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetDeathImpulse(UPARAM(ref)
	                            FGameplayEffectContextHandle& EffectContextHandle, const FVector& InDeathImpulse);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetKnockback(UPARAM(ref)
	                         FGameplayEffectContextHandle& EffectContextHandle, const FVector& InKnockback);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetIsRadialDamage(UPARAM(ref)
	                              FGameplayEffectContextHandle& EffectContextHandle, bool bInIsRadialDamage);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetMinDamageRadius(UPARAM(ref)
	                               FGameplayEffectContextHandle& EffectContextHandle, float InMinDamageRadius);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetMaxDamageRadius(UPARAM(ref)
	                               FGameplayEffectContextHandle& EffectContextHandle, float InMaxDamageRadius);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetDamageOrigin(UPARAM(ref)
	                            FGameplayEffectContextHandle& EffectContextHandle, const FVector& InDamageOrigin);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void GetLivePlayerWithinRadius(const UObject* WorldContextObject, const TArray<AActor*>& IgnoreActors,
	                                      const FVector& Origin, float Radius, TArray<AActor*>& OutActors);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static int32 GetXPRewardForClassAndLevel(UObject* WorldContextObject, const ECharacterClass& CharacterClass,
	                                         int32 CharacterLevel);

	UFUNCTION(BlueprintCallable, Category = "AuraAbilitySystemLibrary|Combat")
	static FGameplayEffectContextHandle ApplyDamageEffect(const FDamageEffectParams& Params, const FVector& InDamageOrigin = FVector::ZeroVector);

	UFUNCTION(BlueprintCallable, Category = "AuraAbilitySystemLibrary|Combat")
	static TArray<AActor*> GetLiveTargetsWithinRadius(const UObject* WorldContextObject,
	                                                  const TArray<AActor*>& IgnoreActors,
	                                                  const FVector& Origin, float Radius);

	UFUNCTION(BlueprintCallable, Category = "AuraAbilitySystemLibrary|Combat")
	static TArray<AActor*> GetClosestActorsFromSource(const AActor* SourceActor, const TArray<AActor*>& TargetActors,
	                                                  int32 MaxTargets);

	UFUNCTION(BlueprintCallable, Category = "AuraAbilitySystemLibrary|Combat")
	static bool AddLooseGameplayTagsToActor(AActor* Actor, const FGameplayTagContainer& GameplayTags);

	UFUNCTION(BlueprintCallable, Category = "AuraAbilitySystemLibrary|Combat")
	static bool RemoveLooseGameplayTagsToActor(AActor* Actor, const FGameplayTagContainer& GameplayTags);
	/* Combat End */

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|GameMechanics")
	static bool IsNotFriend(AActor* FirstActor, AActor* SecondActor);

private:
	static FWidgetControllerParams GetOverlayWidgetControllerParams(const UObject* WorldContextObject,
	                                                                AAuraHUD*& OutAuraHUD);
};
